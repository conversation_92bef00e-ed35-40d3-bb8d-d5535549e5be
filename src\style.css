@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Open+Sans:wght@400;600&display=swap');

*{
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-transform: capitalize;
    transition: all .2s linear;
    text-decoration: none;
}

html{
    font-size: 62.5%;
}

body{
    overflow-x: hidden;
    background-color: #1a1a1a;
    color: #fff;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

.heading, .clients .section-header h2, .section-head h1, .testimonials .section-header h2, .section-head-1 h4{
    margin: 2rem;
    padding-top: 6rem;
    display: inline-block;
    font-size: 3.5rem;
    color: #002e5f;
    position: relative;
    letter-spacing: .2rem;
}

.heading::before, .heading::after, .clients .section-header h2::before, .clients .section-header h2::after, .section-head h1::before, .section-head h1::after, .testimonials .section-header h2::before, .testimonials .section-header h2::after, .section-head-1 h4::before, .section-head-1 h4::after{
    content: '';
    position: absolute;
    height: 2.5rem;
    width: 2.5rem;
    border-top: .4rem solid #002e5f;
    border-left: .4rem solid #002e5f;
}

.faq .heading::before, .faq .heading::after{
    border-top: .4rem solid #00bfff;
    border-left: .4rem solid #00bfff;
}

.heading::before, .clients .section-header h2::before, .section-head h1::before, .testimonials .section-header h2::before, .section-head-1 h4::before{
    top: 5.8rem;
    left: -2rem;
}

.heading::after, .clients .section-header h2::after, .section-head h1::after, .testimonials .section-header h2::after, .section-head-1 h4::after{
    bottom: -.5rem;
    right: -2rem;
    transform: rotate(180deg);
}

.row .btn{
    outline: none;
    border: none;
    border-radius: 5rem;
    background: white;
    border-style: groove;
    border-color: #002e5f;
    font-size: 1.5rem;
    cursor: pointer;
    height: 3.5rem;
    width: 15rem;
    box-shadow: 0 .2rem .5rem rgba(0,0,0,.3);
}

.communicate .btn{
    outline: none;
    border: none;
    border-radius: 5rem;
    background: white;
    border-style: groove;
    border-color: #002e5f;
    font-size: 1.5rem;
    cursor: pointer;
    height: 3.5rem;
    width: 15rem;
    box-shadow: 0 .2rem .5rem rgba(0,0,0,.3);
}

.row .btn:hover{
    letter-spacing: .1rem;
    opacity: .9;
    color: white;
    background: #002e5f;
}

.communicate .btn:hover{
    letter-spacing: .1rem;
    opacity: .9;
    color: white;
    background: #00bfff;
}

:root {
  /* Refined Color Palette */
  --header-primary: #2c3e50;       /* Deep Slate Blue - Darker Base */
  --header-secondary: #3498db;     /* Bright Professional Blue */
  --header-accent: #2980b9;        /* Slightly Deeper Blue for Accents */
  --header-text: #ecf0f1;          /* Soft Light Text */
  --header-hover: #34495e;         /* Slate Blue for Hover States */
}

/* Enhanced Header and Mobile Menu Styles */
.advanced-header {
    background: linear-gradient(135deg, #2c3e50, #3a506b);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 25px;
    height: 90px; /* Increased to accommodate larger logo */
}

.header-logo-section {
    position: relative;
    z-index: 1001;
    padding: 8px 0;
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
}

.logo-image {
    height: 85px; /* Increased significantly from 55px */
    width: auto;
    transition: transform 0.3s ease;
    object-fit: contain;
    filter: brightness(1.2); /* Make logo slightly brighter */
}

.logo-image:hover {
    transform: scale(1.05);
}

/* Ultra Enhanced Mobile Menu */
.mobile-nav {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #2c3e50f5, #3a506bf5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding-top: 100px; /* Adjusted for new header height */
    opacity: 0;
    transform: translateX(-100%);
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    z-index: 1000;
}

.mobile-nav.show {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 20px 30px;
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateX(-30px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.mobile-nav.show .mobile-nav-link {
    transform: translateX(0);
    opacity: 1;
}

/* Enhanced stagger animation */
.mobile-nav.show .mobile-nav-link:nth-child(1) { transition-delay: 0.2s; }
.mobile-nav.show .mobile-nav-link:nth-child(2) { transition-delay: 0.3s; }
.mobile-nav.show .mobile-nav-link:nth-child(3) { transition-delay: 0.4s; }
.mobile-nav.show .mobile-nav-link:nth-child(4) { transition-delay: 0.5s; }
.mobile-nav.show .mobile-nav-link:nth-child(5) { transition-delay: 0.6s; }
.mobile-nav.show .mobile-nav-link:nth-child(6) { transition-delay: 0.7s; }

.mobile-nav-link svg {
    margin-right: 15px;
    font-size: 1.4rem;
    transition: all 0.3s ease;
}

.mobile-nav-link:active {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(0.98);
}

.mobile-nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
    padding-left: 35px;
}

.mobile-nav-link:hover svg {
    transform: scale(1.2) rotate(5deg);
    color: #3498db;
}

/* Enhanced CTA Button */
.mobile-nav-link.nav-cta {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    margin: 20px;
    padding: 15px 25px;
    border-radius: 12px;
    text-align: center;
    justify-content: center;
    border: none;
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.2);
}

.mobile-nav-link.nav-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.3);
    background: linear-gradient(135deg, #27ae60, #219a52);
}

.mobile-nav-link.nav-cta:active {
    transform: translateY(-1px);
}

/* Enhanced Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: transparent;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
    transition: all 0.3s ease;
}

.mobile-menu-toggle svg {
    transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.mobile-menu-toggle:hover svg {
    color: #3498db;
    transform: rotate(90deg);
}

/* Mobile Specific Styles */
@media (max-width: 768px) {
    .header-nav-section {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .header-container {
        padding: 0 15px;
    }

    /* Enhanced Scrollbar */
    .mobile-nav {
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }

    .mobile-nav::-webkit-scrollbar {
        width: 5px;
    }

    .mobile-nav::-webkit-scrollbar-track {
        background: transparent;
    }

    .mobile-nav::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 10px;
    }

    /* Prevent body scroll when menu is open */
    body.menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }

    /* Add smooth transition for menu icon */
    .mobile-menu-toggle.active svg {
        transform: rotate(180deg);
    }

    .logo-image {
        height: 60px; /* Increased mobile size */
        margin-left: -10px; /* Adjust position slightly */
    }

    .mobile-nav {
        padding-top: 100px; /* Adjusted for new header height */
    }

    .header-container {
        height: 85px; /* Adjusted for mobile */
    }
}

/* Add smooth page transitions */
.page-transition {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.home{
    min-height: 100vh;
    width: 100vw;
    background-image: url('/src/images/img1.jpg');
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    text-align: center;
    padding: 0 1rem;
    position: relative;
    overflow: hidden !important;
}

.home h1{
    color: #fff;
    font-size: 5.5rem;
}

.home h2{
    color: #fff;
    font-size: 3rem;
}

.home .wave{
    position: absolute;
    bottom: -.5rem;
    left: 0;
    height: 11rem;
    width: 100%;
    background: url('/src/images/wave.png');
    background-size: 100rem 11rem;
    animation: waves 8s linear infinite;
    background-repeat: repeat-x;
}

.home .wave2{
    animation-direction: reverse;
    animation-duration: 6s;
    opacity: .3;
}

.home .wave3{
    animation-duration: 4s;
    opacity: .5;
}

@keyframes waves{
    0%{
        background-position-x: 0;
    }
    100%{
        background-position-x: 100rem;
    }
}

/* About Section Styles */


.about {
  position: relative;
  z-index: 1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 4rem 2rem;
    position: relative;
    z-index: 2;
}
.section-head {
    text-align: center;
    margin-bottom: 3rem;
}

.section-head .heading {
    font-size: 3.5rem;
    color: #002B5B;
    margin-bottom: 1rem;
}

.section-head p {
    font-size: 2rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

.about-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 4rem;
}

.about-item {
    text-align: center;
    padding: 2rem;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.about-item:hover {
    transform: translateY(-5px);
}

.about-item .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: inline-block;
}

.about-item h6 {
    font-size: 2.2rem;
    margin-bottom: 1rem;
    color: #002B5B;
}

.about-item p {
    font-size: 1.6rem;
    color: #666;
    line-height: 1.6;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stat-item h3 {
    font-size: 3rem;
    color: #002B5B;
    margin: 0.5rem 0;
}

.stat-item p {
    font-size: 1.6rem;
    color: #666;
}

.stat-icon {
    font-size: 2.5rem;
}

@media (max-width: 768px) {
    .about-container,
    .stats-container {
        grid-template-columns: 1fr;
    }
}

.about-grid {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 2rem;
    padding: 2rem;
}

.mission-statement {
    margin: 2rem 0;
}

.highlight-text {
    color: #00bfff;
    font-weight: bold;
    font-size: 1.2rem;
    display: block;
    margin-bottom: 1rem;
}

.commitment-box {
    background: linear-gradient(135deg, #003366 0%, #00bfff 100%);
    padding: 2rem;
    border-radius: 10px;
    color: white;
    margin: 2rem 0;
    text-align: center;
}

.emphasis-text {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.emphasis-subtext {
    font-size: 1.2rem;
    opacity: 0.9;
}

.about-stats {
    display: grid;
    gap: 1rem;
    padding: 1rem;
}

.stat-item {
    background: #f5f5f5;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #003366;
}

.stat-label {
    display: block;
    margin-top: 0.5rem;
    color: #666;
}

@media (max-width: 768px) {
    .about-grid {
        grid-template-columns: 1fr;
    }
    
    .about-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .about-stats {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.about-content {
    animation: fadeInUp 0.8s ease-out;
}

.stat-item {
    animation: fadeInUp 0.8s ease-out;
    animation-delay: calc(var(--i) * 0.2s);
}

.counters {
	background-image: url('/src/images/img2.jpg');
    background-size: cover;
    background-attachment: fixed;
    background-repeat: no-repeat;
	color: #fff;
	padding: 40px 20px;
}

.counters .container {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 30px;
	text-align: center;
}

.counters i {
	color: #fff;
	margin-bottom: 5px;
}

.counters .counter {
	font-size: 45px;
	margin: 10px 0;
}

@media (max-width: 700px) {
	.counters .container {
		grid-template-columns: repeat(2, 1fr);
	}

	.counters .container > div:nth-of-type(1),
	.counters .container > div:nth-of-type(2) {
		border-bottom: 1px lightskyblue solid;
		padding-bottom: 20px;
	}
}

.section-head-1{
    margin-bottom: 60px;
    background-size: 200%;
    background-position: left;

  }
  .section-head-1 p{
    color:#333;
    font-size: 20px;
    line-height: 28px;
    text-align: center;
  }
  .item{
    background:#fff;
    text-align: center;
    padding:30px 25px;
    box-shadow: 0 0 25px rgba(0,0,0,0.07);
    border-radius: 20px;
    margin-bottom: 30px;
    margin-top: -2rem;
    border:5px solid rgba(0,0,0,0.07);
    -webkit-transition:all 0.5s ease 0s;
    transition:all 0.5s ease 0s;
  }
  .item:hover{
    background-image: linear-gradient(to bottom right, #66ffcc 0%, #ffccff 100%);
    background-position: right;
    transition: background-position 3s;
    box-shadow: 0 1px 1px 0 rgba(0,0,0,0.2);
    -webkit-transition:all 0.5s ease 0s;
    transition:all 0.7s ease 0s;
  }
  .item:hover .item,
  .item:hover span.icon{
    background:#fff;
    border-radius: 10px;
    -webkit-transition:all 0.5s ease 0s;
    transition:all 0.5s ease 0s;
  }
  .item h6{
      font-size: 2rem;
  }
  .item:hover h6,
  .item:hover p{
    color:#2b6777;
    -webkit-transition:all 0.5s ease 0s;
    transition:all 0.5s ease 0s;
  }
  .item .icon{
    font-size:40px;
    margin-bottom: 25px;
    color:yellow;
    width:90px;
    height:90px;
    line-height: 96px;
    border-radius: 50px;
  }
  .item .feature_box_col_one{
    background:rgba(247,198,5,0.2);
    color:#52ab98;
  }
  .item .feature_box_col_two{
    background: rgba(255, 77, 28, 0.15);
    color:#52ab98;
  }
  .item .feature_box_col_three{
    background:rgba(0,147,38,0.15);
    color:#52ab98;
  }
  .item .feature_box_col_four{
    background:rgba(0,108,255,0.15);
    color:#52ab98;
  }
  .item .feature_box_col_five{
    background:rgba(146,39,255,0.15);
    color:#52ab98;
  }
  .item .feature_box_col_six{
    background:rgba(23,39,246,0.15);
    color:#52ab98;
  }
  .item p{
    font-size: 15px;
    line-height: 26px;
  }
  .item h6{
    margin-bottom: 20px;
    color:#2f2f2f;
  }   

.communicate{
    text-align: center;
    align-items: center;
    background-image: url('/src/images/img3.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    color: #fff;
    min-height: 35vh;
    padding: 40px 20px;
}  

.communicate h3{
    margin-top: 3rem;
    font-size: 3rem;
}

.communicate p{
    font-size: 2rem;
}


.team{
    min-width: 100vw;
    min-height: 95vh;
    text-align: center;
    background-color: #222;
}

.team .heading{
    color: #00bfff;
}

.team .heading::before, .team .heading::after{
    border-color: #00bfff;
}

.team .row{
    display: inline-block;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.team .row .card{
    height: 35rem;
    width: 30rem;
    background-color: #fff;
    text-align: center;
    margin: 5rem 5rem;
    position: relative;
    overflow: hidden;
    -webkit-box-reflect: below 5px linear-gradient(transparent 70%, #0004);
    transition: 0.5s;
}

.team .row .card:hover{
    transform: translateY(-10px);
    cursor: pointer;
}


.team .row .card .image{
    margin: 1rem 0;
    padding-top: 4rem;
}

.team .row .card .image img{
    height: 13rem;
    width: 13rem;
    border-radius: 50%;
    border: .5rem solid #fff;
    box-shadow: 0 0 .5rem rgba(0,0,0,.3);
    object-fit: cover;
}

.team .row .card .info h3{
    font-size: 2rem;
    color: #333;
}

.team .row .card .info span{
    font-size: 1.8rem;
    color: #00bfff;
}

.team .row .card .info .icons a{
    margin-top: 4rem;
    padding-top: 0 1rem;
    font-size: 2rem;
    color: #333;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.team .row .card .info .icons .fa-facebook-f:hover, .footer .icons .fa-facebook-f:hover{
    color: #4267B2;
    text-decoration: none;
}

.team .row .card .info .icons .fa-twitter:hover, .footer .icons .fa-twitter:hover{
    color: #1DA1F2;
    text-decoration: none;
}

.team .row .card .info .icons .fa-instagram:hover, .footer .icons .fa-instagram:hover{
    color: #C13584;
    text-decoration: none;
}

.team .row .card .info .icons .fa-linkedin:hover, .footer .icons .fa-linkedin:hover{
    color: #2867B2;
    text-decoration: none;
}

.team .row .card::before, .team .row .card::after{
    content: '';
    position: absolute;
    border-radius: 50%;
    height: 13.5rem;
    width: 13.5rem;
    z-index: -1;
}

.team .row .card::before{
    background: #00bfff;
    top: -3rem;
    right: -4rem;
}

.team .row .card::after{
    background: #ccc;
    bottom: -3rem;
    left: -4rem;
}

.contact {
    position: relative;
    padding: 6rem 0;
    background-color: #f4f6f9;
    overflow: hidden;
}

.contact-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/src/images/img3.jpg'); /* Replace with your background image */
    background-size: cover;
    background-position: center;
    z-index: 1;
    opacity: 0.1;
}

.contact-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 46, 95, 0.9) 0%, rgba(0, 191, 255, 0.9) 100%);
    z-index: 2;
    opacity: 0.8;
}

.contact .container {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}
.contact .heading {
    display: block;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    width: fit-content; /* Ensures the heading is only as wide as its content */
}

.contact .heading::before,
.contact .heading::after {
    /* Existing positioning will remain the same */
    position: absolute;
}

.contact .heading::before {
    top: 5.8rem;
    left: -2rem;
}

.contact .heading::after {
    bottom: -.5rem;
    right: -2rem;
    transform: rotate(180deg);
}

.contact-in {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-map {
    width: 100%;
    height: 500px;
}

.contact-map iframe {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.contact-form {
    padding: 3rem;
    background: white;
}

.contact-form .form-group {
    margin-bottom: 1.5rem;
}

.contact-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #002e5f;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1.6rem;
    transition: all 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    border-color: #00bfff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.2);
}

.contact-form-btn {
    width: 100%;
    padding: 1.2rem;
    background: linear-gradient(45deg, #002e5f, #00bfff);
    color: white;
    border: none;
    border-radius: 50px;
    font-size: 1.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.contact-form-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.status-message {
    text-align: center;
    padding: 1rem;
    margin-bottom: 2rem;
    border-radius: 8px;
    font-size: 1.6rem;
}

.status-message.success {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.status-message.error {
    background-color: rgba(244, 67, 54, 0.2);
    color: #F44336;
}

@media (max-width: 768px) {
    .contact-in {
        grid-template-columns: 1fr;
    }

    .contact-map {
        height: 300px;
    }

    .contact-form {
        padding: 2rem;
    }
}

.footer {
    position: relative;
    padding: 0 0 15px 0;
    background: #333;
}

.footer .footer-top {
    background: #002e5f;
    padding: 30px 0 20px 0;
}

.footer .footer-top .container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer .footer-top .footer-info,
.footer .footer-top .footer-links,
.footer .footer-top .footer-contact,
.footer .footer-top .footer-newsletter {
    margin-bottom: 20px;
}

.footer .footer-top .social-links a {
    font-size: 14px;
    display: inline-block;
    background: #ffffff;
    color: #00bfff;
    line-height: 1;
    padding: 7px 0;
    margin-right: 4px;
    text-align: center;
    width: 28px;
    height: 28px;
    transition: 0.3s;
}

.footer .footer-top h4 {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    text-transform: uppercase;
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 15px;
    letter-spacing: 0.5px;
}

.footer .footer-top .footer-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer .footer-top .footer-links ul li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 5px 0;
}

.footer .footer-top .footer-links ul a {
    font-size: 13px;
    color: #ffffff;
    transition: 0.3s;
}

.footer .footer-top .footer-links ul a:hover {
    color: #00bfff;
    padding-left: 5px;
}

.footer .footer-top .footer-contact p {
    color: #ffffff;
    line-height: 22px;
    font-size: 13px;
    margin-bottom: 5px;
}

.footer .footer-top .footer-newsletter {
    margin-bottom: 0;
}

.footer .footer-top .footer-newsletter input[type="email"] {
    padding: 4px 8px;
    width: 60%;
    border: 1px solid #ffffff;
    background: transparent;
    color: #ffffff;
    font-size: 13px;
}

.footer .footer-top .footer-newsletter input[type="submit"] {
    border: 0;
    width: 40%;
    padding: 4px 0;
    text-align: center;
    color: black;
    border: 1px solid #ffffff;
    background: #ffffff;
    transition: 0.3s;
    cursor: pointer;
    font-size: 13px;
}

.footer .footer-top .footer-newsletter input[type="submit"]:hover {
    color: #ffffff;
    background: #00bfff;
    border: 1px solid #00bfff;
    letter-spacing: .2rem;
}

.footer .footer-top .footer-newsletter p {
    color: #ffffff;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 10px;
}

.footer .credit,
.footer .copyright {
    text-align: center;
    padding-top: 15px;
    font-size: 13px;
    color: #fff;
}

@media (max-width: 768px) {
    .footer .footer-top .container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .footer .footer-top {
        padding: 20px 0 10px 0;
    }
    
    .footer .footer-top .footer-info,
    .footer .footer-top .footer-links,
    .footer .footer-top .footer-contact,
    .footer .footer-top .footer-newsletter {
        margin-bottom: 15px;
        text-align: center;
    }
    
    .footer .footer-top h4::before {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .footer .footer-top .footer-links ul {
        text-align: center;
    }
}

.back-to-top {
    position: fixed;
    display: none;
    background-color: #00bfff;
    width: 45px;
    height: 45px;
    text-align: center;
    right: 15px;
    bottom: 15px;
    transition: all 0.3s ease;
    z-index: 9;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top i {
    color: #ffffff;
    font-size: 24px;
    display: block;
    line-height: 1;
}

.back-to-top:hover {
    background-color: #002e5f;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.back-to-top:hover i {
    color: #ffffff;
}

@media (max-width: 768px) {
    .back-to-top {
        width: 40px;
        height: 40px;
    }
    
    .back-to-top i {
        font-size: 20px;
    }
}

.faq{
    min-height: 70vh;
    width: 100vw;
    text-align: center;
    padding: 0 2rem;
    background: url('/src/images/faq.jpg');
    background-repeat: no-repeat;
    background-size: cover;
}

.faq .row{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 2rem;
}

.faq .row .accordion-container{
    width: 50%;
    text-align: left;
}

.faq .row .accordion{
    margin-left: 1rem;
    margin-right: 2rem;
}

.faq .row .accordion-container .accordion .accordion-header{
    background-color: #00bfff;
    margin: 1rem 0;
    box-shadow: .1rem .1rem .3rem rgba(0,0,0,.3);
    cursor: pointer;
    margin-left: 1rem;
    margin-right: 2rem;
}

.faq .row .accordion-container .accordion .accordion-header span{
    display: inline-block;
    text-align: center;
    height: 4rem;
    width: 5rem;
    line-height: 4rem;
    font-size: 2rem;
    background: #333;
    color: #fff;
    clip-path: polygon(0% 0%,75% 0%,100% 50%,75% 100%,0% 100%); 
}

.faq .row .accordion-container .accordion .accordion-header h3{
    display: inline;
    color: #333;
    font-weight: 400;
    padding-left: .5rem;
    font-size: 1.5rem;
}

.faq .row .accordion-container .accordion .accordion-body{
    padding: 1rem;
    color: #444;
    box-shadow: .1rem .1rem .3rem rgba(0,0,0,.3);
    background-color: #fff;
    font-size: 1.3rem;
    display: none;
    margin-left: 1rem;
    margin-right: 2rem;
}

@media (max-width: 1200px){
    .faq{
        min-height: 70vh;
    }
}        

@media (max-width: 1000px){
    html{
        font-size: 50%;
    }

    .header .logo img{
        width: 20%;
        height: 3rem;
        top: 0;
        left: 0;
        background-size: cover;
    }

    .header .fa-bars{
        display: block;
        color: 	white;
        margin-right: 1rem;
    }

    .header .fa-bars:hover{
        color: #00bfff;
    }

    .header .navbar{
        position: fixed;
        top: -120%;
        left: 0;
        height: auto;
        width: 100%;
        background-color: white;
        z-index: 1000;
        border-top: .1rem solid rgba(0,0,0,.3);
    }

    .header .navbar ul{
        height: 100%;
        width: 100%;
        flex-flow: column;
    }

    .header .navbar ul li{
        margin: 1rem 0;
    }

    .header .navbar ul li a{
        color: grey;
        font-size: 2.4rem;
    }

    .header .fa-times{
        transform: rotate(90deg);
    }

    .header .nav-toggle{
        top: 5.8rem;
    }

    .home h1{
        color: #fff;
        font-size: 4rem;
    }
    
    .home h2{
        color: #fff;
        font-size: 2rem;
    }

    .about{
        min-height: 38vh;
        width: 100vw;
        margin-top: -3rem;
        margin-bottom: -2rem;
        top: -2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        line-height: 6rem;
    }
    
    .about .row{
        line-height: 2rem;
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 0 4rem;
    }
    
    .about .row .content{
        text-align: center;
    }
    
    .about .row .content h3{
        font-size: 2rem;
        color: var(--color);
    }
    
    .about .row .content p{
        font-size: 1.3rem;
        color: #333;
        padding: 1rem 0;
    } 

    .clients {
        position: relative;
        padding: 90px 0;
        text-align: center;
        margin-top: -15rem;
        margin-bottom: -8rem;
    }

    .clients .section-header p {
        padding-bottom: 10px;
        margin-top: 2.5rem;
        text-align: center;
        font-size: 2rem;
    }

    .team{
        min-height: auto;
    }

    .team .row{
        flex-direction: column;
    }

    .contact{
        text-align: center;
        align-items: center;
    }
    
    .contact .heading{
        margin-bottom: 3rem;
        margin-top: -2rem;
    }

    .contact-in
		{
			width: 80%;
			height: auto;
			margin: auto auto 5rem auto;
			display: flex;
			flex-wrap: wrap;
			padding: 10px;
			border-radius: 10px;
			background: #fff;
			box-shadow: 0px 0px 10px 0px #666;
		}

		.contact-map
		{
			width: 100%;
			height: auto;
			flex: 50%;
		}
		.contact-map iframe
		{
			width: 100%;
			height: 100%;
		}
		.contact-form
		{
			width: 100%;
			height: auto;
			flex: 50%;
			text-align: left;
		}
		.contact-form-txt
		{
			width: 90%;
			height: 20px;
			color: #000;
			border: 1px solid #bcbcbc;
			border-radius: 50px;
			outline: none;
			margin-bottom: 20px;
			padding: 15px;
		}
        .contact-form-email
		{
            margin-left: 2rem;
			width: 90%;
			height: 20px;
			color: #000;
			border: 1px solid #bcbcbc;
			border-radius: 50px;
			outline: none;
			margin-bottom: 20px;
			padding: 15px;
		}
		.contact-form-txt::placeholder
		{
			color: #aaa;
            font-size: 1.5rem;
		}
        .contact-form-email::placeholder
		{
			color: #aaa;
            font-size: 1.5rem;
		}
		.contact-form-txtarea
		{
            margin-left: 2rem;
			width: 90%;
			height: 110px;
			color: #000;
			border: 1px solid #bcbcbc;
			border-radius: 10px;
			outline: none;
			margin-bottom: 20px;
			padding: 15px;
			font-family: 'Poppins', sans-serif;
		}
		.contact-form-txtarea::placeholder
		{
			color: #aaa;
            font-size: 1.5rem;
		}

		.contact-form-btn
		{
            margin-left: 2rem;
            outline: none;
            border: none;
            border-radius: 5rem;
            background: white;
            border-style: groove;
            border-color: #002e5f;
            font-size: 1.5rem;
            cursor: pointer;
            height: 3.5rem;
            width: 12rem;
            box-shadow: 0 .2rem .5rem rgba(0,0,0,.3);
		}
        .contact-form-btn:hover{
            letter-spacing: .1rem;
            opacity: .9;
            color: white;
            background: #002e5f;
        }
		.contact-form-phone
		{
            margin-left: 2rem;
			width: 90%;
			height: 20px;
			color: #000;
			border: 1px solid #bcbcbc;
			border-radius: 50px;
			outline: none;
			margin-bottom: 20px;
			padding: 15px;
		}
		.contact-form-phone::placeholder
		{
			color: #aaa;
            font-size: 1.5rem;
		}

        .faq{
            padding: 0;
            min-height: 60vh;
        }
    
        .faq .row{
            padding: 0 1.5rem;
            flex-flow: column;
        }
    
        .faq .row .accordion-container{
            width: 100%;
        }
}

/* Services Section */
.services {
    padding: 8rem 0;
    background: #f2f2f2;
    position: relative;
    background-image: url('/src/images/img1.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1;
}

.services .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.services .section-head {
    text-align: center;
    margin-bottom: 5rem;
}

.services .section-head p {
    font-size: 1.8rem;
    color: #666;
    max-width: 800px;
    margin: 2rem auto 0;
    line-height: 1.6;
}

.services-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.service-item {
    background: #fff;
    padding: 3rem 2rem;
    border-radius: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
}

.service-item:hover {
    transform: translateY(-1rem);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.2);
}

.service-item .icon {
    width: 8rem;
    height: 8rem;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
    font-size: 3rem;
    color: #002e5f;
    transition: all 0.3s ease;
}

.service-item:hover .icon {
    background: #002e5f;
    color: #fff;
}

.service-item h6 {
    font-size: 2.2rem;
    color: #002e5f;
    margin-bottom: 1.5rem;
}

.service-item p {
    font-size: 1.6rem;
    color: #666;
    line-height: 1.6;
}

/* Media Queries for Services */
@media (max-width: 768px) {
    .services-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-item {
        padding: 2rem 1.5rem;
    }

    .services .section-head p {
        font-size: 1.6rem;
    }
}

/* Portfolio Section */
.portfolio {
    padding: 8rem 0;
    background: #fff;
}

.portfolio .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.portfolio .section-head {
    text-align: center;
    margin-bottom: 5rem;
}

.portfolio .section-head p {
    font-size: 1.8rem;
    color: #666;
    max-width: 800px;
    margin: 2rem auto 0;
    line-height: 1.6;
}

.projects-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.project-item {
    background: #f8f9fa;
    padding: 3rem 2rem;
    border-radius: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
}

.project-item:hover {
    transform: translateY(-1rem);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.2);
    background: #002e5f;
}

.project-item .icon {
    width: 8rem;
    height: 8rem;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 50%;
    font-size: 3rem;
    color: #002e5f;
    transition: all 0.3s ease;
}

.project-item:hover .icon {
    background: #00bfff;
    color: #fff;
}

.project-item h6 {
    font-size: 2.2rem;
    color: #002e5f;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.project-item p {
    font-size: 1.6rem;
    color: #666;
    line-height: 1.6;
    transition: all 0.3s ease;
}

.project-item:hover h6,
.project-item:hover p {
    color: #fff;
}

/* Media Queries for Portfolio */
@media (max-width: 768px) {
    .projects-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .project-item {
        padding: 2rem 1.5rem;
    }

    .portfolio .section-head p {
        font-size: 1.6rem;
    }
}

/* Join Us Section Styles */
.joinus {
  width: 80%;
  height: auto;
  margin: 5rem auto 5rem auto;
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 0px 10px 0px #666;
}

.joinus-form {
  margin-top: .5rem;
  width: 100%;
  height: auto;
  flex: 50%;
  text-align: left;
}

.joinus-form-txt,
.joinus-form-experience,
.joinus-form-email,
.joinus-form-phone {
  margin-left: 2rem;
  width: 96.5%;
  height: 40px;
  color: #000;
  border: 1px solid #bcbcbc;
  border-radius: 50px;
  outline: none;
  margin-bottom: 20px;
  padding: 15px;
}

.joinus-form-txt::placeholder,
.joinus-form-experience::placeholder,
.joinus-form-email::placeholder,
.joinus-form-phone::placeholder {
  color: #aaa;
  font-size: 1.5rem;
}

.radio-class {
  margin-left: 1rem;
  width: 110%;
  margin-top: -2rem;
  height: 40px;
  color: #000;
  margin-bottom: 5rem;
  padding: 15px;
}

.radio {
  margin-top: .75rem;
  font-size: 1.5rem;
  margin-right: 9.5rem;
}

.joinus-form-txtarea {
  margin-left: 2rem;
  width: 96.5%;
  height: 130px;
  color: #000;
  border: 1px solid #bcbcbc;
  border-radius: 10px;
  outline: none;
  margin-bottom: 20px;
  padding: 15px;
  font-family: 'Poppins', sans-serif;
  font-size: 1.5rem;
}

.joinus-form-txtarea::placeholder {
  color: #aaa;
  font-size: 1.5rem;
}

.file {
  margin-left: 1rem;
  width: 100%;
  margin-top: -2.5rem;
  height: 40px;
  color: #000;
  margin-bottom: 5rem;
  padding: 15px;
}

.upload {
  margin-top: .75rem;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.joinus-form-btn {
  margin-left: 2rem;
  outline: none;
  border: none;
  border-radius: 5rem;
  background: white;
  border-style: groove;
  border-color: #002e5f;
  font-size: 1.2rem;
  cursor: pointer;
  height: 3.5rem;
  margin-top: 1rem;
  margin-bottom: .5rem;
  width: 15rem;
  box-shadow: 0 .2rem .5rem rgba(0,0,0,.3);
}

.joinus-form-btn:hover {
  letter-spacing: .1rem;
  opacity: .9;
  color: white;
  background: #002e5f;
}

/* Career heading styles */
.joinus-heading {
  text-align: center;
}

.joinus-heading .heading {
  padding-top: 6rem;
  display: inline-block;
  font-size: 3.5rem;
  color: #002e5f;
  position: relative;
  letter-spacing: .2rem;
  margin: -2rem auto 2rem auto;
}

.joinus-heading p {
  font-size: 2rem;
  color: #333;
}

/* Responsive styles for Join Us section */
@media (max-width: 1500px) {
  .joinus {
    width: 80%;
  }

  .joinus-form-txt,
  .joinus-form-experience,
  .joinus-form-email,
  .joinus-form-phone,
  .joinus-form-txtarea,
  .joinus-form-btn {
    margin-left: 1rem;
  }

  .radio-class {
    margin-left: 0;
    height: auto;
    margin-bottom: auto;
  }

  .radio {
    margin-right: 5rem;
  }

  .file {
    margin-left: 0;
  }
}

@media (max-width: 1000px) {
  .joinus-heading .heading {
    font-size: 3rem;
  }

  .joinus-heading p {
    font-size: 1.8rem;
  }

  .joinus {
    width: 90%;
  }

  .radio {
    margin-right: 3rem;
  }
}

/* Add these styles to handle page transitions and layouts */
.page-transition {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Adjust section padding for standalone pages */
.about,
.services,
.portfolio,
.contact {
    padding-top: 80px; /* Adjust based on your header height */
    min-height: calc(100vh - 80px); /* Ensures full page height minus header */
}

/* Add page-specific background styles */
.about { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); }
.services { background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%); }
.portfolio { background: linear-gradient(135deg, #f5f5f5 0%, #fff 100%); }
.contact { background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%); }

/* Responsive adjustments */
@media (max-width: 768px) {
    .about,
    .services,
    .portfolio,
    .contact {
        padding-top: 60px;
    }
}

.about-header {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.heading {
  text-align: center;
  font-size: 3.5rem;
  color: #002B5B;
}

.home-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.btn {
  position: relative;
  display: inline-block;
  padding: 0;
  overflow: hidden;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  font-family: var(--primary-font);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.btn-secondary {
  background: transparent;
  color: #007bff;
  border: 2px solid #007bff;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.2);
  transition: all 0.3s ease;
  z-index: 0;
}

.btn:hover::before {
  left: 0;
}

.btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 8px rgba(0,0,0,0.15);
}

.btn-icon {
  margin-right: 10px;
  font-size: 1.2em;
  display: flex;
  align-items: center;
}

.btn-text {
  position: relative;
  z-index: 2;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #0056b3, #003d82);
}

.btn-secondary:hover {
  background-color: #007bff;
  color: white;
}

@media (max-width: 768px) {
  .home-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .btn-content {
    padding: 10px 20px;
  }
}

/* Typography and Headings */
body {
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 100px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-content {
  text-align: center;
  max-width: 800px;
}

.home-title {
  font-size: 4.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 20px;
  background: linear-gradient(45deg, #ffffff, #e0e0e0);  /* White to light gray gradient */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.2);
}

.home-subtitle {
  font-size: 2rem; /* Larger, more prominent subtitle */
  font-weight: 400;
  color: #e4dcdcf3;
  line-height: 1.6;
  margin-bottom: 30px;
}

.home-cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.home-cta-button {
  font-size: 1.2rem;
  padding: 12px 24px;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.home-cta-primary {
  background-color: #007bff;
  color: white;
}

.home-cta-secondary {
  background-color: transparent;
  color: #007bff;
  border: 2px solid #007bff;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .home-title {
    font-size: 3rem; /* Smaller on mobile */
  }

  .home-subtitle {
    font-size: 1.5rem; /* Smaller on mobile */
  }

  .home-cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}

.application-form-wrapper {
  background-color: #f4f6f9;
  padding: 2rem 0;
  min-height: 100vh;
}

.application-form-container {
  max-width: 700px;
  margin: 0 auto;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.application-form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.application-form-header h2 {
  color: #333;
  margin-bottom: 0.5rem;
}

.application-form-header p {
  color: #666;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-input, .form-textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  transition: border-color 0.3s ease;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #4a90e2;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 1.5rem;
}

.submit-application-btn, .cancel-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-application-btn {
  background-color: #4a90e2;
  color: white;
}

.submit-application-btn:hover {
  background-color: #357abd;
}

.cancel-btn {
  background-color: #f4f4f4;
  color: #333;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.file-input-wrapper {
  display: flex;
  align-items: center;
}

.file-upload-btn {
  background-color: #f4f4f4;
  border: 1px dashed #ddd;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  flex-grow: 1;
  text-align: center;
}

.advanced-footer {
  background-color: #1a1a2e;
  color: #ffffff;
  padding: 4rem 0;
  font-size: 16px; /* Base font size increased */
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 2rem;
}

.footer-section {
  padding: 1rem;
}

.footer-section h3, .footer-section h4 {
  margin-bottom: 1.5rem;
  color: #4ecdc4;
  font-size: 2.2rem; /* Larger section titles */
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-links a {
  color: #ffffff;
  font-size: 2.2rem; /* Larger social icons */
  transition: color 0.3s ease;
}

.social-links a:hover {
  color: #4ecdc4;
}

.newsletter-form {
  display: flex;
}

.newsletter-form input {
  flex-grow: 1;
  padding: 0.8rem;
  font-size: 1.6rem; /* Larger input text */
}

.newsletter-form button {
  background-color: #4ecdc4;
  color: #1a1a2e;
  border: none;
  padding: 0.8rem 1.2rem;
  font-size: 1.6rem; /* Larger button text */
}

.footer-bottom {
  margin-top: 2rem;
  border-top: 1px solid #333;
  padding-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom-links a {
  color: #4ecdc4;
  margin-left: 1rem;
  font-size: 1.4rem; /* Slightly larger bottom footer text */
}

@media (max-width: 768px) {
  .footer-grid {
    grid-template-columns: 1fr 1fr;
  }

  .advanced-footer {
    font-size: 14px; /* Slightly smaller on mobile */
  }

  .footer-section h3 {
    font-size: 2rem;
  }

  .footer-section h4 {
    font-size: 1.6rem;
  }

  .footer-section p,
  .links-grid ul li,
  .contact-info p,
  .newsletter-form input,
  .newsletter-form button {
    font-size: 1.4rem;
  }
}

@media (max-width: 480px) {
  .footer-grid {
    grid-template-columns: 1fr;
  }
}

/* Quick Links Specific Styles */
.quick-links {
  position: relative;
}

.quick-links::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50%;
  height: 2px;
  background-color: #4ecdc4;
  transition: width 0.3s ease;
}

.links-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.links-grid ul {
  list-style: none;
  padding: 0;
}

.links-grid ul li {
  position: relative;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.links-grid ul a {
  color: #ffffff;
  text-decoration: none;
  font-size: 1.6rem;
  display: inline-block;
  position: relative;
  padding-left: 15px;
  transition: all 0.3s ease;
}

.links-grid ul a::before {
  content: '→';
  position: absolute;
  left: 0;
  opacity: 0;
  color: #4ecdc4;
  transition: all 0.3s ease;
}

.links-grid ul a:hover {
  color: #4ecdc4;
  padding-left: 25px;
}

.links-grid ul a:hover::before {
  opacity: 1;
  left: -15px;
}

.links-grid ul li::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #4ecdc4;
  transition: width 0.3s ease;
}

.links-grid ul li:hover::after {
  width: 100%;
}

/* Hover effect for entire section */
.quick-links:hover::before {
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .links-grid {
    grid-template-columns: 1fr;
  }

  .links-grid ul a {
    font-size: 1.4rem;
  }
}

/* Advanced Header Styles */
.advanced-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 80px; /* Increased from default */
}

.advanced-header.scrolled {
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-link {
  text-decoration: none;
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 2.5rem; /* Increased from previous size */
  font-weight: 800; /* Bolder weight */
  color: white;
  letter-spacing: -0.05em; /* Slight letter spacing */
  transition: all 0.3s ease;
}

.logo-text:hover {
  transform: scale(1.05);
  color: rgba(255,255,255,0.9);
}

.logo-tagline {
  font-size: 1rem; /* Slightly larger tagline */
  color: rgba(255,255,255,0.7);
  margin-left: 0.5rem;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem; /* Increased gap between links */
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.5rem; /* Significantly increased from previous size */
  display: flex;
  align-items: center;
  gap: 0.7rem;
  position: relative;
  transition: all 0.3s ease;
}

.nav-link svg {
  font-size: 1.3rem; /* Larger icon size to match text */
  margin-right: 0.5rem;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -7px; /* Slightly lower underline */
  left: 0;
  width: 100%;
  height: 3px; /* Slightly thicker underline */
  background: white;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-link:hover {
  color: rgba(255,255,255,0.8);
  transform: translateY(-3px); /* Subtle lift on hover */
}

.nav-link:hover::after {
  transform: scaleX(1);
}

.nav-link.nav-cta {
  font-size: 1.4rem; /* Larger CTA text */
  padding: 0.8rem 1.5rem; /* More spacious padding */
  border-radius: 35px; /* Slightly more rounded */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .logo-text {
    font-size: 2.2rem;
  }

  .nav-link {
    font-size: 1.3rem;
  }

  .nav-link svg {
    font-size: 1.2rem;
  }

  .nav-link.nav-cta {
    font-size: 1.2rem;
    padding: 0.7rem 1.3rem;
  }
}

.nav-cta {
  background-color: #3498db;
  color: white;
  padding: 0.7rem 1.2rem;
  border-radius: 5px;
  font-size: 1.2rem;
  transition: background-color 0.3s ease;
}

.nav-cta:hover {
  background-color: #2980b9;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.dark-mode-toggle,
.mobile-menu-toggle {
  cursor: pointer;
  font-size: 1.5rem;
  color: #2c3e50;
  transition: color 0.3s ease;
}

.dark-mode-toggle:hover,
.mobile-menu-toggle:hover {
  color: #3498db;
}

/* Dark Mode Styles */
.advanced-header.dark-mode {
  background-color: rgba(26, 26, 46, 0.95);
  color: #ffffff;
}

.dark-mode .logo-text {
  color: #4ecdc4;
}

.dark-mode .nav-link {
  color: #ffffff;
}

.dark-mode .nav-link:hover {
  color: #4ecdc4;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-menu.mobile-open {
    display: block;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: linear-gradient(
        135deg, 
        rgba(44, 62, 80, 0.98) 0%, 
        rgba(52, 152, 219, 0.98) 100%
    );
    backdrop-filter: blur(8px);
  }

  .nav-links {
    flex-direction: column;
    align-items: stretch;
  }

  .nav-link {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    color: #ffffff;  /* Ensuring text is visible */
  }

  .advanced-header {
    height: 70px; /* Slightly smaller on mobile */
  }

  .logo-container .logo-image {
    max-height: 50px; /* Smaller logo on mobile */
  }

  .nav-links .nav-link {
    font-size: 1.2rem; /* Slightly smaller on mobile */
  }
}

.team-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 3rem;
  padding: 4rem 0;
  background-color: rgba(0, 0, 0, 0.03);
}

.team-member-card {
  width: 300px; /* Decreased width */
  background: white;
  border-radius: 25px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  position: relative;
  transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  background-clip: padding-box;
  border: 2px solid rgba(82, 121, 111, 0.1);
}

.team-member-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg, 
    rgba(82, 121, 111, 0.05), 
    rgba(52, 78, 65, 0.05)
  );
  opacity: 0;
  transition: opacity 0.6s ease;
  transform: rotate(-45deg);
  z-index: 1;
}

.team-member-card:hover::before {
  opacity: 1;
}

.team-member-card:hover {
  transform: translateY(-20px) rotateX(7deg) scale(1.06);
  box-shadow: var(--hover-shadow);
  border-color: rgba(37, 117, 252, 0.2);
}

.member-image-container {
  height: 300px; /* Reduced from 380px */
  position: relative;
  overflow: hidden;
  transition: all 0.6s ease;
}

.member-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: grayscale(20%) brightness(0.95);
  transition: all 0.6s ease;
}

.team-member-card:hover .member-image {
  transform: scale(1.15);
  filter: grayscale(0%) brightness(1);
}

.member-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg, 
    rgba(52, 78, 65, 0.7) 0%, 
    rgba(82, 121, 111, 0.6) 100%
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.6s ease;
  padding: 2rem;
  text-align: center;
}

.team-member-card:hover .member-overlay {
  opacity: 1;
}

.overlay-content {
  transform: translateY(30px);
  opacity: 0;
  transition: all 0.6s ease;
}

.team-member-card:hover .overlay-content {
  transform: translateY(0);
  opacity: 1;
}

.member-details {
  padding: 2rem;
  text-align: center;
  position: relative;
  z-index: 2;
}

.member-name {
  background: linear-gradient(135deg, #34495e 0%, #3498db 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 800; /* Increased from 700 */
  font-size: 2.2rem; /* Increased from previous size */
  letter-spacing: -0.05rem; /* Slight letter spacing for cleaner look */
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.team-member-card:hover .member-name {
  background: linear-gradient(135deg, #2c3e50 0%, #2980b9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.05rem; /* Slight expansion on hover */
  transform: scale(1.05);
}

.member-role {
  font-size: 1.4rem; /* Increased from previous size */
  color: var(--background-soft);
  font-weight: 500;
  font-style: italic;
  margin-bottom: 1rem;
}

.member-role::after {
  content: '';
  position: absolute;
  bottom: -0.7rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--primary-gradient);
}

.member-bio {
  font-size: 1.2rem; /* Increased from previous size */
  line-height: 1.8; /* Improved readability */
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.7rem;
  margin-bottom: 1.8rem;
}

.skill-tag {
  background: linear-gradient(135deg, #f6f6f6 0%, #e0e0e0 100%);
  color: #34495e;
  padding: 0.6rem 1rem;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.4s ease;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(52, 73, 94, 0.1);
}

.skill-tag::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg, 
    rgba(52, 73, 94, 0.05), 
    rgba(52, 73, 94, 0.1)
  );
  transform: rotate(-45deg);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.skill-tag:hover {
  transform: scale(1.1) translateY(-5px);
  box-shadow: 0 6px 10px rgba(0,0,0,0.15);
  color: #2c3e50;
}

.skill-tag:hover::before {
  opacity: 1;
}

.member-social-links {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
}

.social-link {
  position: relative;
  color: #34495e;
  font-size: 2rem;
  transition: all 0.4s ease;
  display: inline-block;
}

.social-link::before {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(
    135deg, 
    #34495e 0%, 
    #3498db 100%
  );
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.4s ease;
}

.social-link:hover {
  color: #3498db;
  transform: translateY(-7px);
}

.social-link:hover::before {
  transform: scaleX(1);
  transform-origin: left;
}

/* Tooltip Effect for Social Links */
.social-link {
  position: relative;
}

.social-link::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%) scale(0);
  background-color: #2c3e50;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 10;
}

.social-link:hover::after {
  transform: translateX(-50%) scale(1);
  opacity: 1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .skills-list {
    gap: 0.5rem;
  }

  .skill-tag {
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
  }

  .member-social-links {
    gap: 1.5rem;
  }

  .social-link {
    font-size: 1.8rem;
  }
}

.btn.btn-secondary {
  background: transparent;
  border: 2px solid #0d1ad1;
  color: #fff;
  transition: all 0.3s ease;
}

.btn.btn-secondary:hover {
  background: #5648d1e8;
  color: #f8f8f8;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

/* Mobile Menu Styles */
.mobile-nav {
    display: none; /* Hidden by default */
    position: absolute;
    top: 70px; /* Header height */
    left: 0;
    right: 0;
    background-color: #2c3e50;
    padding: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 999;
}

.mobile-nav.show {
    display: block;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav-link svg {
    margin-right: 10px;
    font-size: 1.2rem;
}

.mobile-nav-link span {
    font-size: 1rem;
}

.mobile-menu-toggle {
    display: none; /* Hidden on desktop */
    background: transparent;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 10px;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .header-nav-section {
        display: none; /* Hide desktop nav */
    }

    .mobile-menu-toggle {
        display: block; /* Show hamburger menu */
    }

    .mobile-nav-link.nav-cta {
        background-color: #2ecc71;
        margin: 10px 20px;
        border-radius: 5px;
        text-align: center;
        justify-content: center;
    }
}

/* Prevent body scroll when mobile menu is open */
body.menu-open {
    overflow: hidden;
}

/* Enhanced Mobile Menu Styles */
.mobile-nav {
    display: none;
    position: absolute;
    top: 70px;
    left: 0;
    right: 0;
    background-color: #2c3e50;
    padding: 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    z-index: 999;
}

.mobile-nav.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 18px 25px;
    color: white;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    transform: translateX(-10px);
    opacity: 0;
    transition: all 0.3s ease;
}

.mobile-nav.show .mobile-nav-link {
    transform: translateX(0);
    opacity: 1;
}

/* Stagger animation for menu items */
.mobile-nav.show .mobile-nav-link:nth-child(1) { transition-delay: 0.1s; }
.mobile-nav.show .mobile-nav-link:nth-child(2) { transition-delay: 0.2s; }
.mobile-nav.show .mobile-nav-link:nth-child(3) { transition-delay: 0.3s; }
.mobile-nav.show .mobile-nav-link:nth-child(4) { transition-delay: 0.4s; }
.mobile-nav.show .mobile-nav-link:nth-child(5) { transition-delay: 0.5s; }
.mobile-nav.show .mobile-nav-link:nth-child(6) { transition-delay: 0.6s; }

.mobile-nav-link svg {
    margin-right: 15px;
    font-size: 1.3rem;
    transition: transform 0.3s ease;
}

.mobile-nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
    padding-left: 30px;
}

.mobile-nav-link:hover svg {
    transform: scale(1.2);
}

.mobile-nav-link span {
    font-size: 1.1rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Enhanced Mobile Menu Toggle Button */
.mobile-menu-toggle {
    display: none;
    background: transparent;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 10px;
    transition: transform 0.3s ease;
    position: relative;
    z-index: 1000;
}

.mobile-menu-toggle:hover {
    transform: scale(1.1);
}

.mobile-menu-toggle:active {
    transform: scale(0.95);
}

/* Special styling for CTA button in mobile menu */
.mobile-nav-link.nav-cta {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    margin: 15px 20px;
    border-radius: 8px;
    text-align: center;
    justify-content: center;
    border: none;
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.2);
}

.mobile-nav-link.nav-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(46, 204, 113, 0.3);
    background: linear-gradient(135deg, #27ae60, #219a52);
    padding-left: 25px; /* Prevent shifting on hover */
}

.mobile-nav-link.nav-cta:active {
    transform: translateY(-1px);
}

/* Mobile Styles */
@media (max-width: 768px) {
    .header-nav-section {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Animated hamburger to close transition */
    .mobile-menu-toggle svg {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .mobile-menu-toggle:hover svg {
        color: #3498db;
    }

    /* Add overlay when menu is open */
    .mobile-nav::before {
        content: '';
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }

    .mobile-nav.show::before {
        opacity: 1;
    }

    /* Improve scroll behavior */
    .mobile-nav.show {
        max-height: calc(100vh - 70px);
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }

    /* Custom scrollbar styling */
    .mobile-nav.show::-webkit-scrollbar {
        width: 5px;
    }

    .mobile-nav.show::-webkit-scrollbar-track {
        background: transparent;
    }

    .mobile-nav.show::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 20px;
    }
}

/* Add smooth transition for body when menu opens */
body {
    transition: overflow 0.3s ease;
}

body.menu-open {
    overflow: hidden;
}

/* Ultra Enhanced Mobile Menu */
.mobile-nav {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #2c3e50f5, #3a506bf5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding-top: 85px;
    opacity: 0;
    transform: translateX(-100%);
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    z-index: 1000;
}

.mobile-nav.show {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 20px 30px;
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateX(-30px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.mobile-nav.show .mobile-nav-link {
    transform: translateX(0);
    opacity: 1;
}

/* Enhanced stagger animation */
.mobile-nav.show .mobile-nav-link:nth-child(1) { transition-delay: 0.2s; }
.mobile-nav.show .mobile-nav-link:nth-child(2) { transition-delay: 0.3s; }
.mobile-nav.show .mobile-nav-link:nth-child(3) { transition-delay: 0.4s; }
.mobile-nav.show .mobile-nav-link:nth-child(4) { transition-delay: 0.5s; }
.mobile-nav.show .mobile-nav-link:nth-child(5) { transition-delay: 0.6s; }
.mobile-nav.show .mobile-nav-link:nth-child(6) { transition-delay: 0.7s; }

.mobile-nav-link svg {
    margin-right: 15px;
    font-size: 1.4rem;
    transition: all 0.3s ease;
}

.mobile-nav-link:active {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(0.98);
}

.mobile-nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
    padding-left: 35px;
}

.mobile-nav-link:hover svg {
    transform: scale(1.2) rotate(5deg);
    color: #3498db;
}

/* Enhanced CTA Button */
.mobile-nav-link.nav-cta {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    margin: 20px;
    padding: 15px 25px;
    border-radius: 12px;
    text-align: center;
    justify-content: center;
    border: none;
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.2);
}

.mobile-nav-link.nav-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.3);
    background: linear-gradient(135deg, #27ae60, #219a52);
}

.mobile-nav-link.nav-cta:active {
    transform: translateY(-1px);
}

/* Enhanced Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: transparent;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
    transition: all 0.3s ease;
}

.mobile-menu-toggle svg {
    transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.mobile-menu-toggle:hover svg {
    color: #3498db;
    transform: rotate(90deg);
}

/* Mobile Specific Styles */
@media (max-width: 768px) {
    .header-nav-section {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .header-container {
        padding: 0 15px;
    }

    /* Enhanced Scrollbar */
    .mobile-nav {
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }

    .mobile-nav::-webkit-scrollbar {
        width: 5px;
    }

    .mobile-nav::-webkit-scrollbar-track {
        background: transparent;
    }

    .mobile-nav::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 10px;
    }

    /* Prevent body scroll when menu is open */
    body.menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }

    /* Add smooth transition for menu icon */
    .mobile-menu-toggle.active svg {
        transform: rotate(180deg);
    }
}

/* Add smooth page transitions */
.page-transition {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Team Member Achievement Styles */
.member-overlay .overlay-content {
    padding: 20px;
    color: white;
}

.member-overlay .overlay-content h4 {
    font-size: 20px;  /* Increased from default */
    margin-bottom: 15px;
    font-weight: 600;
    color: #fff;
}

.member-overlay .overlay-content ul {
    list-style: none;
    padding: 0;
}

.member-overlay .overlay-content li {
    font-size: 14px;  /* Increased from default */
    line-height: 1.6;
    margin-bottom: 12px;
    padding-left: 25px;
    position: relative;
}

.member-overlay .overlay-content li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #2ecc71;
    font-weight: bold;
    font-size: 20px;  /* Increased checkmark size */
}

/* Hover effect for better readability */
.member-overlay:hover .overlay-content {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}