.joinus-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  padding-top: 80px;
  min-height: calc(100vh - 80px - 300px);
}

.joinus-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 4rem 0;
  background: linear-gradient(135deg, #003366 0%, #00bfff 100%);
  color: white;
  border-radius: 10px;
}

.joinus-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.joinus-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.positions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.position-card {
  background: white;
  padding: 3rem 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.position-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.position-icon {
  font-size: 4rem;
  margin-bottom: 2rem;
  display: block;
}

.position-card h3 {
  color: #002e5f;
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.position-card p {
  color: #666;
  font-size: 1.4rem;
  margin-bottom: 2rem;
}

.apply-btn {
  background: linear-gradient(135deg, #002e5f, #004a99);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.4rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  background: linear-gradient(135deg, #004a99, #00bfff);
  transform: translateY(-2px);
}

.application-form-container {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.application-form-container h2 {
  text-align: center;
  color: #003366;
  margin-bottom: 2rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
}

.form-group textarea {
  height: 120px;
  resize: vertical;
}

.file-upload-container {
  position: relative;
}

.file-input {
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  z-index: -1;
}

.file-upload-label {
  display: block;
  padding: 0.8rem;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-label:hover {
  background: #eee;
  border-color: #00bfff;
}

.submit-button {
  display: block;
  width: 100%;
  padding: 1rem;
  background: #003366;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 2rem;
}

.submit-button:hover {
  background: #00bfff;
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .joinus-container {
    padding: 1rem;
  }
  
  .positions-grid {
    grid-template-columns: 1fr;
  }
}

.joinus-section {
    min-height: 100vh;
    padding-top: 8rem;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
                url('../images/img2.jpg') center/cover no-repeat fixed;
    text-align: center;
}

.joinus-section .heading {
    display: inline-block;
    color: #fff;
    margin: 2rem auto;
    padding-top: 6rem;
    font-size: 3.5rem;
    position: relative;
    letter-spacing: .2rem;
}

.joinus-section .heading::before,
.joinus-section .heading::after {
    content: '';
    position: absolute;
    height: 2.5rem;
    width: 2.5rem;
    border-top: .4rem solid #00bfff;
    border-left: .4rem solid #00bfff;
}

.joinus-section .heading::before {
    top: 5.8rem;
    left: -2rem;
}

.joinus-section .heading::after {
    bottom: -.5rem;
    right: -2rem;
    transform: rotate(180deg);
}

.positions-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.application-form {
    max-width: 800px;
    margin: 4rem auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.application-form h2 {
    color: #002e5f;
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.8rem;
    position: relative;
    padding-bottom: 1rem;
}

.application-form h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #002e5f, #00bfff);
}

.application-form form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 1rem;
}

.application-form form > * {
    grid-column: span 2;
}

.application-form .contact-form-txt,
.application-form .contact-form-phone,
.application-form .contact-form-email,
.application-form select {
    width: 100%;
    padding: 1.2rem 2rem;
    border: 2px solid #eee;
    border-radius: 8px;
    font-size: 1.6rem;
    transition: all 0.3s ease;
    background: white;
    color: #333;
    margin-bottom: 0;
}

.application-form .contact-form-txt:focus,
.application-form .contact-form-phone:focus,
.application-form .contact-form-email:focus,
.application-form select:focus {
    border-color: #00bfff;
    box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
    outline: none;
}

.application-form .contact-form-txtarea {
    width: 100%;
    min-height: 150px;
    padding: 1.2rem 2rem;
    border: 2px solid #eee;
    border-radius: 8px;
    font-size: 1.6rem;
    transition: all 0.3s ease;
    resize: vertical;
    grid-column: span 2;
}

.application-form .contact-form-txtarea:focus {
    border-color: #00bfff;
    box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
    outline: none;
}

.file-upload {
    grid-column: span 2;
    text-align: center;
    margin: 2rem 0;
}

.file-upload label.contact-form-btn {
    display: inline-block;
    padding: 1.2rem 3rem;
    background: white;
    color: #002e5f;
    border: 2px solid #002e5f;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.6rem;
    transition: all 0.3s ease;
    margin: 0;
}

.file-upload label.contact-form-btn:hover {
    background: #002e5f;
    color: white;
    transform: translateY(-2px);
}

.application-form .contact-form-btn[type="submit"] {
    grid-column: span 2;
    padding: 1.5rem;
    background: #002e5f;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 2rem;
    width: 100%;
}

.application-form .contact-form-btn[type="submit"]:hover {
    background: #00bfff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 191, 255, 0.3);
}

/* Add floating labels */
.input-group {
    position: relative;
    grid-column: span 1;
}

.input-group input:not(:placeholder-shown) + label,
.input-group input:focus + label {
    transform: translateY(-2.5rem) scale(0.8);
    color: #002e5f;
}

/* Responsive design */
@media (max-width: 768px) {
    .application-form {
        margin: 2rem;
        padding: 2rem;
    }

    .application-form form {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .input-group {
        grid-column: span 2;
    }

    .application-form h2 {
        font-size: 2.4rem;
    }
}

/* Add subtle animation */
.application-form {
    animation: formAppear 0.5s ease-out;
}

@keyframes formAppear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add hover effects for form elements */
.application-form .contact-form-txt:hover,
.application-form .contact-form-phone:hover,
.application-form .contact-form-email:hover,
.application-form select:hover,
.application-form .contact-form-txtarea:hover {
    border-color: #00bfff;
    background-color: rgba(255, 255, 255, 0.9);
}

/* Animation for position cards */
.item {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Application Form Styles */
.contact-in.application-form {
    max-width: 1000px;
    margin: 4rem auto;
    padding: 4rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.form-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.contact-in.application-form h2 {
    color: #002e5f;
    text-align: center;
    font-size: 3.5rem;
    margin-bottom: 4rem;
    position: relative;
    padding-bottom: 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.contact-in.application-form h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 4px;
    background: linear-gradient(to right, #002e5f, #00bfff);
    border-radius: 2px;
}

.form-row {
    display: flex;
    gap: 2.5rem;
    margin-bottom: 2.5rem;
    position: relative;
}

.form-group {
    flex: 1;
    position: relative;
}

.form-group.full-width {
    flex: 0 0 100%;
}

.contact-form-txt,
.contact-form-phone,
.contact-form-email,
select.contact-form-txt {
    width: 100%;
    padding: 1.8rem 2rem;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 1.6rem;
    color: #333;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.contact-form-txtarea {
    width: 100%;
    min-height: 180px;
    padding: 1.8rem 2rem;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 1.6rem;
    resize: vertical;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Input focus and hover states */
.contact-form-txt:hover,
.contact-form-phone:hover,
.contact-form-email:hover,
.contact-form-txtarea:hover,
select.contact-form-txt:hover {
    border-color: #00bfff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 191, 255, 0.1);
}

.contact-form-txt:focus,
.contact-form-phone:focus,
.contact-form-email:focus,
.contact-form-txtarea:focus,
select.contact-form-txt:focus {
    border-color: #002e5f;
    box-shadow: 0 0 0 4px rgba(0, 47, 95, 0.1);
    outline: none;
    transform: translateY(-2px);
}

/* File upload styling */
.file-upload {
    text-align: center;
    margin: 3rem 0;
    position: relative;
}

.file-input {
    display: none;
}

.upload-btn {
    display: inline-block;
    padding: 1.5rem 4rem;
    background: linear-gradient(135deg, #002e5f, #004a99);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: 1.6rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 47, 95, 0.2);
}

.upload-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 47, 95, 0.3);
    background: linear-gradient(135deg, #004a99, #00bfff);
}

.upload-btn:active {
    transform: translateY(-1px);
}

/* Submit button styling */
.submit-btn {
    width: 100%;
    padding: 1.8rem;
    background: linear-gradient(135deg, #002e5f, #004a99);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(0, 47, 95, 0.2);
}

.submit-btn:hover {
    background: linear-gradient(135deg, #004a99, #00bfff);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 47, 95, 0.3);
}

.submit-btn:active {
    transform: translateY(-1px);
}

/* Input placeholder styling */
::placeholder {
    color: #999;
    opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
    .contact-in.application-form {
        margin: 2rem;
        padding: 2.5rem;
    }

    .form-row {
        flex-direction: column;
        gap: 2rem;
    }

    .contact-in.application-form h2 {
        font-size: 2.8rem;
        margin-bottom: 3rem;
    }

    .contact-form-txt,
    .contact-form-phone,
    .contact-form-email,
    select.contact-form-txt,
    .contact-form-txtarea {
        padding: 1.5rem;
        font-size: 1.4rem;
    }
}

/* Form animation */
.contact-in.application-form {
    animation: formAppear 0.8s ease-out;
}

@keyframes formAppear {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add a subtle hover effect to the entire form */
.contact-in.application-form:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
    transform: translateY(-5px);
    transition: all 0.4s ease;
} 