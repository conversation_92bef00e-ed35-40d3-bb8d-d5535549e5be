/* Application Form Specific Styles */
.application-form-wrapper {
    background: linear-gradient(135deg, #f4f6f9 0%, #e9ecef 100%);
    padding: 6rem 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.application-form-container {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    transition: all 0.3s ease;
}

.application-form-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.application-form-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.application-form-header h2 {
    font-size: 3rem;
    color: #002e5f;
    margin-bottom: 1rem;
    letter-spacing: -0.05rem;
}

.application-form-header p {
    font-size: 1.6rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.application-form-header::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background-color: #00bfff;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-size: 1.4rem;
    color: #333;
    font-weight: 500;
}

.form-group.full-width {
    grid-column: span 2;
}

.form-input, 
.form-textarea,
.file-upload-btn {
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1.8rem;
    transition: all 0.3s ease;
}

.form-input:focus, 
.form-textarea:focus {
    outline: none;
    border-color: #00bfff;
    box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.2);
}

.form-textarea {
    min-height: 150px;
}

.file-input-wrapper {
    position: relative;
    overflow: hidden;
}

.file-input {
    position: absolute;
    left: -9999px;
}

.file-upload-btn:hover {
    background-color: #f0f0f0;
    border-color: #00bfff;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    gap: 1rem;
}

.submit-application-btn, 
.cancel-btn {
    flex: 1;
    padding: 1.2rem;
    border: none;
    border-radius: 50px;
    font-size: 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-application-btn {
    background: linear-gradient(45deg, #002e5f, #00bfff);
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.submit-application-btn:hover {
    background: linear-gradient(45deg, #00bfff, #002e5f);
    transform: translateY(-3px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.cancel-btn {
    background-color: #f4f4f4;
    color: #333;
    border: 2px solid #ddd;
}

.cancel-btn:hover {
    background-color: #e0e0e0;
    transform: translateY(-3px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-group.full-width {
        grid-column: span 1;
    }

    .application-form-container {
        padding: 2rem;
        margin: 0 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .submit-application-btn, 
    .cancel-btn {
        width: 100%;
        margin-bottom: 1rem;
    }
} 