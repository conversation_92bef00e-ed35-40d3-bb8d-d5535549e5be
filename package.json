{"name": "electra-gen", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "mongoose": "^8.12.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "web-vitals": "^2.1.4", "yocto-queue": "^1.2.0"}, "scripts": {"dev": "react-scripts start", "start": "react-scripts start", "build": "CI=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": "18.x"}}